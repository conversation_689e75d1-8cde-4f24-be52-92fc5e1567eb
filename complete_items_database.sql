-- Complete SQL script to create and populate the items table
-- Based on the Excel file: Copy of Multilevel_BOS_Report_Collector_revS7530VB_level.xlsx
-- 
-- This script:
-- 1. Creates the items table with item_id and item_description columns
-- 2. Includes all the data extracted from the Excel file
-- 
-- Data source analysis:
-- - Excel file has 6,217 total rows
-- - Headers are in row 10
-- - Data starts from row 11
-- - Column 3: "Mark Number" (used as item_id)
-- - Column 6: "Name" (used as item_description)
-- - Extracted 2,790 unique records
-- - Skipped 706 duplicate records

-- =====================================================
-- STEP 1: CREATE TABLE STRUCTURE
-- =====================================================

-- Drop table if it exists (optional - remove if you want to keep existing data)
-- DROP TABLE IF EXISTS items;

-- Create the items table
CREATE TABLE items (
    item_id VARCHAR(100) PRIMARY KEY,
    item_description VARCHAR(500) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE items IS 'Items table containing item IDs and descriptions from the Multilevel BOS Report';
COMMENT ON COLUMN items.item_id IS 'Item identifier from Mark Number column (Column 3 in Excel)';
COMMENT ON COLUMN items.item_description IS 'Item description from Name column (Column 6 in Excel)';
COMMENT ON COLUMN items.created_date IS 'Timestamp when the record was created';
COMMENT ON COLUMN items.updated_date IS 'Timestamp when the record was last updated';

-- Create indexes for better performance
CREATE INDEX idx_items_description ON items(item_description);
CREATE INDEX idx_items_created_date ON items(created_date);

-- =====================================================
-- STEP 2: INSERT DATA
-- =====================================================

-- Note: The actual INSERT statements are in the file 'insert_items_data.sql'
-- You can either:
-- 1. Run this script first, then run insert_items_data.sql
-- 2. Or copy the INSERT statements from insert_items_data.sql and append them here

-- To load the data, execute the following command after running this script:
-- \i insert_items_data.sql  (for PostgreSQL)
-- SOURCE insert_items_data.sql;  (for MySQL)
-- .read insert_items_data.sql  (for SQLite)

-- =====================================================
-- STEP 3: VERIFICATION QUERIES
-- =====================================================

-- Check the total number of records
-- SELECT COUNT(*) as total_records FROM items;

-- Check for any duplicate item_ids (should be 0 due to PRIMARY KEY constraint)
-- SELECT item_id, COUNT(*) as count 
-- FROM items 
-- GROUP BY item_id 
-- HAVING COUNT(*) > 1;

-- Sample data preview
-- SELECT * FROM items LIMIT 10;

-- Search for specific items (example)
-- SELECT * FROM items WHERE item_description LIKE '%M70012RJ%';

-- =====================================================
-- ADDITIONAL NOTES
-- =====================================================

-- 1. The item_id column uses VARCHAR(100) to accommodate various ID formats
-- 2. The item_description column uses VARCHAR(500) for longer descriptions
-- 3. Timestamps are included for audit purposes
-- 4. Indexes are created for better query performance
-- 5. All data has been extracted from the Excel file and deduplicated

-- Statistics from extraction:
-- - Total unique records: 2,790
-- - Duplicate records skipped: 706
-- - Source file: Copy of Multilevel_BOS_Report_Collector_revS7530VB_level.xlsx
-- - Generated on: 2025-09-15
