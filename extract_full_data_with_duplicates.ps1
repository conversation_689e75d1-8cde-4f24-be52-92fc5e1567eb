# PowerShell script to extract row_number, name and description from Excel file
# and generate SQL INSERT statements (KEEPING ALL DUPLICATES)

param(
    [string]$ExcelPath = "Copy of Multilevel_BOS_Report_Collector_revS7530VB_level.xlsx",
    [string]$OutputFile = "insert_full_data_with_duplicates.sql"
)

try {
    Write-Host "Starting Excel data extraction for row_number, name and description columns..."
    Write-Host "PRESERVING ALL DUPLICATES - No deduplication will be performed"
    
    # Create Excel COM object
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    # Open the workbook
    $workbook = $excel.Workbooks.Open((Resolve-Path $ExcelPath).Path)
    $worksheet = $workbook.Worksheets.Item(1)
    
    # Get the used range
    $usedRange = $worksheet.UsedRange
    $rowCount = $usedRange.Rows.Count
    
    Write-Host "Total rows in Excel: $rowCount"
    Write-Host "Headers are in row 10, data starts from row 11"
    Write-Host "Extracting Column 1 (Row Number), Column 6 (Name) and Column 12 (Description)"
    Write-Host "Keeping ALL records including duplicates"
    
    # Initialize output
    $sqlStatements = @()
    $sqlStatements += "-- SQL INSERT statements for row_number, name and description columns"
    $sqlStatements += "-- File: $ExcelPath"
    $sqlStatements += "-- Generated on: $(Get-Date)"
    $sqlStatements += "-- Column 1: Row Number, Column 6: Name, Column 12: Description"
    $sqlStatements += "-- ALL DUPLICATES PRESERVED - No deduplication performed"
    $sqlStatements += ""
    $sqlStatements += "-- Clear existing data (optional - uncomment if needed)"
    $sqlStatements += "-- DELETE FROM items_full_data;"
    $sqlStatements += ""
    $sqlStatements += "-- Insert data from Excel file (all records including duplicates)"
    
    # Counters
    $processedCount = 0
    $skippedEmptyCount = 0
    $nullRowNumberCount = 0
    $nullNameCount = 0
    $nullDescriptionCount = 0
    
    # Process data rows (starting from row 11)
    for ($row = 11; $row -le $rowCount; $row++) {
        # Get row_number from column 1
        $rowNumber = $worksheet.Cells.Item($row, 1).Value2
        # Get name from column 6
        $name = $worksheet.Cells.Item($row, 6).Value2
        # Get description from column 12
        $description = $worksheet.Cells.Item($row, 12).Value2
        
        # Track null values for statistics
        if (-not $rowNumber) { $nullRowNumberCount++ }
        if (-not $name) { $nullNameCount++ }
        if (-not $description) { $nullDescriptionCount++ }
        
        # Skip only if ALL three values are empty
        if (-not $rowNumber -and -not $name -and -not $description) {
            $skippedEmptyCount++
            continue
        }
        
        # Convert to string and clean up (handle nulls)
        $rowNumberStr = if ($rowNumber) { $rowNumber.ToString().Trim() } else { "NULL" }
        $nameStr = if ($name) { "'$($name.ToString().Trim() -replace "'", "''")'" } else { "NULL" }
        $descriptionStr = if ($description) { "'$($description.ToString().Trim() -replace "'", "''")'" } else { "NULL" }
        
        $processedCount++
        
        # Create SQL INSERT statement
        $sqlStatement = "INSERT INTO items_full_data (row_number, name, description, excel_row_position) VALUES ($rowNumberStr, $nameStr, $descriptionStr, $row);"
        $sqlStatements += $sqlStatement
        
        # Progress indicator
        if ($processedCount % 100 -eq 0) {
            Write-Host "Processed $processedCount records..."
        }
    }
    
    # Add final statistics
    $sqlStatements += ""
    $sqlStatements += "-- Statistics:"
    $sqlStatements += "-- Total records processed: $processedCount"
    $sqlStatements += "-- Records with NULL row_number: $nullRowNumberCount"
    $sqlStatements += "-- Records with NULL name: $nullNameCount"
    $sqlStatements += "-- Records with NULL description: $nullDescriptionCount"
    $sqlStatements += "-- Completely empty records skipped: $skippedEmptyCount"
    $sqlStatements += "-- NO DEDUPLICATION PERFORMED - All duplicates preserved"
    $sqlStatements += "-- Generated on: $(Get-Date)"
    
    # Write to output file
    $sqlStatements | Out-File -FilePath $OutputFile -Encoding UTF8
    
    Write-Host ""
    Write-Host "Extraction completed successfully!"
    Write-Host "Total records processed: $processedCount"
    Write-Host "Records with NULL row_number: $nullRowNumberCount"
    Write-Host "Records with NULL name: $nullNameCount"
    Write-Host "Records with NULL description: $nullDescriptionCount"
    Write-Host "Completely empty records skipped: $skippedEmptyCount"
    Write-Host "ALL DUPLICATES PRESERVED - No deduplication performed"
    Write-Host "SQL file generated: $OutputFile"
    
    # Close and cleanup
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($excel) {
        try {
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        } catch {}
    }
}
