# Python code for notebook - Create bill_of_source_Item_description table
# Copy and paste this into your notebook cell

# Assuming you have a database connection (cursor/engine)
# Replace 'cursor' or 'engine' with your actual connection variable

# SQL to create the table
create_table_sql = """
DROP TABLE IF EXISTS bill_of_source_Item_description;

CREATE TABLE bill_of_source_Item_description (
    id SERIAL PRIMARY KEY,
    row_number INTEGER,
    name VARCHAR(200),
    description TEXT,
    excel_row_position INTEGER,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_bos_row_number ON bill_of_source_Item_description(row_number);
CREATE INDEX idx_bos_name ON bill_of_source_Item_description(name);
CREATE INDEX idx_bos_description ON bill_of_source_Item_description(description);
"""

# Execute the SQL
try:
    # For psycopg2 cursor:
    cursor.execute(create_table_sql)
    connection.commit()
    
    # OR for SQLAlchemy engine:
    # engine.execute(create_table_sql)
    
    # OR for pandas with SQLAlchemy:
    # pd.read_sql(create_table_sql, engine)
    
    print("✅ Table 'bill_of_source_Item_description' created successfully!")
    
    # Verify table creation
    verify_sql = "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_name = 'bill_of_source_item_description';"
    
    # Execute verification (adjust based on your connection type)
    cursor.execute(verify_sql)
    result = cursor.fetchone()
    
    if result[0] > 0:
        print("✅ Table verified - ready for data insertion!")
    else:
        print("❌ Table verification failed")
        
except Exception as e:
    print(f"❌ Error creating table: {e}")

# Next step: Insert data using the INSERT statements from insert_bill_of_source_data.sql
