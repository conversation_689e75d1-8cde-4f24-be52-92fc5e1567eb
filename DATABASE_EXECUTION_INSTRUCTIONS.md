# Database Execution Instructions

## Table Name: `bill_of_source_Item_description`

You now have everything ready to create and populate the table on your database!

## Files Ready for Database Execution

### 1. **EXECUTE_ON_DATABASE_bill_of_source_Item_description.sql**
- **Run this first** on your database
- Creates the table `bill_of_source_Item_description`
- Sets up indexes and comments
- Contains verification queries

### 2. **insert_bill_of_source_data.sql**
- **Run this second** on your database
- Contains all 6,207 INSERT statements
- Loads all your Excel data (with duplicates preserved)

## Step-by-Step Database Execution

### Step 1: Create the Table
```sql
-- Execute this file on your database:
\i EXECUTE_ON_DATABASE_bill_of_source_Item_description.sql

-- Or copy and paste the contents into your database query tool
```

### Step 2: Insert the Data
```sql
-- Execute this file on your database:
\i insert_bill_of_source_data.sql

-- Or copy and paste the contents into your database query tool
```

### Step 3: Verify the Data
```sql
-- Check total records (should be 6,207)
SELECT COUNT(*) as total_records FROM bill_of_source_Item_description;

-- Preview the data
SELECT * FROM bill_of_source_Item_description ORDER BY row_number LIMIT 10;
```

## Table Structure

```sql
CREATE TABLE bill_of_source_Item_description (
    id SERIAL PRIMARY KEY,              -- Auto-incrementing ID
    row_number INTEGER,                 -- Excel row numbers
    name VARCHAR(200),                  -- Item names
    description TEXT,                   -- Item descriptions
    excel_row_position INTEGER,         -- Original Excel row reference
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Sample Data

Your table will contain data like this:

| id | row_number | name | description | excel_row_position |
|----|------------|------|-------------|-------------------|
| 1 | 1 | M70012RJ | Sand Bag | 11 |
| 2 | 2 | 5582359 | Sandbag Collector | 12 |
| 3 | 3 | 5628480 | Sandbag - 1.65kg - | 13 |
| 4 | 4 | 5628479 | Sandbag - 0.5kg - | 14 |
| 5 | 5 | M7001NA | 1.5T TDI Posterior Array | 15 |

## Database Compatibility

These scripts work with:
- ✅ **PostgreSQL** (recommended)
- ✅ **MySQL** (may need to change SERIAL to AUTO_INCREMENT)
- ✅ **SQL Server** (may need to change SERIAL to IDENTITY)
- ✅ **SQLite** (may need to change SERIAL to INTEGER PRIMARY KEY)

## Quick Commands for Different Databases

### PostgreSQL
```bash
psql -d your_database -f EXECUTE_ON_DATABASE_bill_of_source_Item_description.sql
psql -d your_database -f insert_bill_of_source_data.sql
```

### MySQL
```bash
mysql -u username -p your_database < EXECUTE_ON_DATABASE_bill_of_source_Item_description.sql
mysql -u username -p your_database < insert_bill_of_source_data.sql
```

### SQL Server (using sqlcmd)
```bash
sqlcmd -S server -d database -i EXECUTE_ON_DATABASE_bill_of_source_Item_description.sql
sqlcmd -S server -d database -i insert_bill_of_source_data.sql
```

## Useful Queries After Data Loading

```sql
-- Find all duplicates for a specific name
SELECT * FROM bill_of_source_Item_description 
WHERE name = 'M70012RJ' 
ORDER BY row_number;

-- Count duplicates by name
SELECT name, COUNT(*) as count 
FROM bill_of_source_Item_description 
GROUP BY name 
HAVING COUNT(*) > 1
ORDER BY count DESC;

-- Search by description
SELECT * FROM bill_of_source_Item_description 
WHERE description LIKE '%Sand%';

-- Get items by row number range
SELECT * FROM bill_of_source_Item_description 
WHERE row_number BETWEEN 1 AND 10 
ORDER BY row_number;
```

## Success Verification

After execution, you should have:
- ✅ Table `bill_of_source_Item_description` created
- ✅ 6,207 records inserted
- ✅ All duplicates preserved
- ✅ Proper indexes for fast querying
- ✅ Complete data from your Excel file

## Need Help?

If you encounter any issues:
1. Check your database type and adjust SERIAL if needed
2. Ensure you have CREATE TABLE permissions
3. Verify the database connection
4. Check for any syntax errors specific to your database system

The table is now ready for use with all your Excel data!
