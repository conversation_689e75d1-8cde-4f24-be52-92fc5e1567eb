import torch
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM
from colpali_engine.models import ColQwen2
from colpali_engine.models import ColQwen2Processor
from PIL import Image
from typing import List, Optional
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ColPaliDeepSeekRAG:
    """
    A RAG system that combines ColPali for visual document understanding
    with DeepSeek R2 for text generation.
    """

    def __init__(self, device: Optional[str] = None):
        """
        Initialize the ColPali + DeepSeek R2 RAG system.

        Args:
            device: Device to use ('cuda', 'cpu', or None for auto-detection)
        """
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Using device: {self.device}")

        # Initialize models
        self._load_colpali()
        self._load_deepseek()

        # Document index for retrieval
        self.document_embeddings = {}
        self.document_images = {}

    def _load_colpali(self):
        """Load ColQwen2 model and processor for visual document understanding."""
        try:
            logger.info("Loading ColQwen2 model...")
            self.colpali_model = ColQwen2.from_pretrained(
                "vidore/colqwen2-v1.0",
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None
            )
            self.colpali_processor = ColQwen2Processor.from_pretrained("vidore/colqwen2-v1.0")

            if self.device == "cpu":
                self.colpali_model = self.colpali_model.to(self.device)

            self.colpali_model.eval()
            logger.info("ColQwen2 model loaded successfully")

        except Exception as e:
            logger.error(f"Error loading ColQwen2: {e}")
            raise

    def _load_deepseek(self):
        """Load DeepSeek R2 model for text generation."""
        try:
            logger.info("Loading DeepSeek R2 model...")

            # Use a smaller DeepSeek R2 model that's more accessible
            model_name = "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B"

            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.llm_model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None,
                trust_remote_code=True
            )

            if self.device == "cpu":
                self.llm_model = self.llm_model.to(self.device)

            # Set pad token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            self.llm_model.eval()
            logger.info("DeepSeek R2 model loaded successfully")

        except Exception as e:
            logger.error(f"Error loading DeepSeek R2: {e}")
            raise

    def index_document(self, image_path: str, doc_id: str):
        """
        Index a document image for later retrieval.

        Args:
            image_path: Path to the document image
            doc_id: Unique identifier for the document
        """
        try:
            logger.info(f"Indexing document: {doc_id}")

            # Load and process image
            image = Image.open(image_path).convert("RGB")

            # Process with ColPali to get embeddings
            inputs = self.colpali_processor.process_images([image])

            with torch.no_grad():
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
                embeddings = self.colpali_model(**inputs)

            # Store embeddings and image
            self.document_embeddings[doc_id] = embeddings.cpu()
            self.document_images[doc_id] = image

            logger.info(f"Document {doc_id} indexed successfully")

        except Exception as e:
            logger.error(f"Error indexing document {doc_id}: {e}")
            raise

    def retrieve_relevant_documents(self, query: str, top_k: int = 3) -> List[str]:
        """
        Retrieve the most relevant documents for a given query.

        Args:
            query: User query
            top_k: Number of top documents to retrieve

        Returns:
            List of document IDs ranked by relevance
        """
        if not self.document_embeddings:
            logger.warning("No documents indexed")
            return []

        try:
            # Process query with ColPali
            query_inputs = self.colpali_processor.process_queries([query])

            with torch.no_grad():
                query_inputs = {k: v.to(self.device) for k, v in query_inputs.items()}
                query_embedding = self.colpali_model(**query_inputs)

            # Calculate similarities with all documents
            similarities = {}
            for doc_id, doc_embedding in self.document_embeddings.items():
                doc_embedding = doc_embedding.to(self.device)

                # ColPali uses late interaction scoring
                similarity = self._calculate_late_interaction_score(
                    query_embedding, doc_embedding
                )
                similarities[doc_id] = similarity.item()

            # Sort by similarity and return top_k
            sorted_docs = sorted(similarities.items(), key=lambda x: x[1], reverse=True)
            return [doc_id for doc_id, _ in sorted_docs[:top_k]]

        except Exception as e:
            logger.error(f"Error retrieving documents: {e}")
            return []

    def _calculate_late_interaction_score(self, query_emb, doc_emb):
        """Calculate late interaction score between query and document embeddings."""
        # Simplified late interaction scoring
        # In practice, this would be more sophisticated
        query_emb = query_emb.squeeze(0)  # Remove batch dimension
        doc_emb = doc_emb.squeeze(0)      # Remove batch dimension

        # Calculate cosine similarity
        query_norm = torch.nn.functional.normalize(query_emb, dim=-1)
        doc_norm = torch.nn.functional.normalize(doc_emb, dim=-1)

        # Max pooling over document patches for each query token
        similarities = torch.matmul(query_norm, doc_norm.transpose(-2, -1))
        max_similarities = torch.max(similarities, dim=-1)[0]

        # Sum over query tokens
        return torch.sum(max_similarities)

    def generate_response(self, query: str, context_docs: List[str] = None) -> str:
        """
        Generate a response using DeepSeek R2 based on the query and retrieved documents.

        Args:
            query: User query
            context_docs: List of document IDs to use as context

        Returns:
            Generated response
        """
        try:
            # Build context from retrieved documents
            context = ""
            if context_docs:
                context = f"Based on the following document(s), "
                if len(context_docs) == 1:
                    context += f"please answer the question.\n\n"
                else:
                    context += f"please answer the question.\n\n"

            # Create prompt for DeepSeek R2
            prompt = f"{context}Question: {query}\n\nAnswer:"

            # Tokenize and generate
            inputs = self.tokenizer(prompt, return_tensors="pt", padding=True, truncation=True)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            with torch.no_grad():
                outputs = self.llm_model.generate(
                    **inputs,
                    max_new_tokens=512,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )

            # Decode response
            response = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            )

            return response.strip()

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return f"Error generating response: {str(e)}"

    def query_documents(self, query: str, top_k: int = 3) -> dict:
        """
        Complete RAG pipeline: retrieve relevant documents and generate response.

        Args:
            query: User query
            top_k: Number of documents to retrieve

        Returns:
            Dictionary with retrieved documents and generated response
        """
        logger.info(f"Processing query: {query}")

        # Retrieve relevant documents
        relevant_docs = self.retrieve_relevant_documents(query, top_k)

        # Generate response
        response = self.generate_response(query, relevant_docs)

        return {
            "query": query,
            "retrieved_documents": relevant_docs,
            "response": response
        }

# Example usage and testing
def main():
    """Example usage of the ColPali + DeepSeek RAG system."""

    # Initialize the system
    rag_system = ColPaliDeepSeekRAG()

    # Example: Index some documents (you would replace these with your actual document paths)
    # rag_system.index_document("path/to/document1.png", "doc1")
    # rag_system.index_document("path/to/document2.png", "doc2")

    # Example query
    # result = rag_system.query_documents("What is the main topic discussed?")
    # print(f"Query: {result['query']}")
    # print(f"Retrieved docs: {result['retrieved_documents']}")
    # print(f"Response: {result['response']}")

    print("ColPali + DeepSeek R2 RAG system initialized successfully!")
    print("Use the following methods:")
    print("- rag_system.index_document(image_path, doc_id) to add documents")
    print("- rag_system.query_documents(query) to ask questions")

    return rag_system

if __name__ == "__main__":
    main()