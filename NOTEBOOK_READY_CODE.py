# =============================================================================
# NOTEBOOK CODE: Create bill_of_source_Item_description table
# Copy and paste this entire cell into your notebook
# =============================================================================

# SQL to create the table
create_table_sql = """
DROP TABLE IF EXISTS bill_of_source_Item_description;

CREATE TABLE bill_of_source_Item_description (
    id SERIAL PRIMARY KEY,
    row_number INTEGER,
    name VARCHAR(200),
    description TEXT,
    excel_row_position INTEGER,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_bos_row_number ON bill_of_source_Item_description(row_number);
CREATE INDEX idx_bos_name ON bill_of_source_Item_description(name);
CREATE INDEX idx_bos_description ON bill_of_source_Item_description(description);
"""

# =============================================================================
# OPTION 1: If you're using psycopg2 (PostgreSQL)
# =============================================================================
"""
try:
    cursor.execute(create_table_sql)
    connection.commit()
    print("✅ Table 'bill_of_source_Item_description' created successfully!")
except Exception as e:
    print(f"❌ Error: {e}")
"""

# =============================================================================
# OPTION 2: If you're using SQLAlchemy engine
# =============================================================================
"""
try:
    with engine.connect() as conn:
        conn.execute(create_table_sql)
        conn.commit()
    print("✅ Table 'bill_of_source_Item_description' created successfully!")
except Exception as e:
    print(f"❌ Error: {e}")
"""

# =============================================================================
# OPTION 3: If you're using pandas with SQLAlchemy
# =============================================================================
"""
try:
    import pandas as pd
    # Split the SQL into individual statements
    statements = create_table_sql.split(';')
    for stmt in statements:
        if stmt.strip():
            pd.read_sql(stmt, engine)
    print("✅ Table 'bill_of_source_Item_description' created successfully!")
except Exception as e:
    print(f"❌ Error: {e}")
"""

# =============================================================================
# OPTION 4: If you're using sqlite3
# =============================================================================
"""
try:
    cursor.executescript(create_table_sql)
    connection.commit()
    print("✅ Table 'bill_of_source_Item_description' created successfully!")
except Exception as e:
    print(f"❌ Error: {e}")
"""

# =============================================================================
# VERIFICATION: Check if table was created
# =============================================================================
"""
try:
    # For PostgreSQL/MySQL
    verify_sql = "SELECT table_name FROM information_schema.tables WHERE table_name = 'bill_of_source_item_description';"
    
    # For SQLite
    # verify_sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='bill_of_source_Item_description';"
    
    cursor.execute(verify_sql)
    result = cursor.fetchall()
    
    if result:
        print("✅ Table verified successfully!")
        print("📊 Table structure:")
        print("- id: Auto-incrementing primary key")
        print("- row_number: Excel row numbers")
        print("- name: Item names")
        print("- description: Item descriptions")
        print("- excel_row_position: Original Excel row reference")
        print("- created_date: Record creation timestamp")
        print("- updated_date: Record update timestamp")
        print("\n🚀 Ready for data insertion!")
    else:
        print("❌ Table verification failed")
        
except Exception as e:
    print(f"❌ Verification error: {e}")
"""

print("📋 Code ready! Uncomment the section that matches your database connection type.")
print("💡 Available options:")
print("   1. psycopg2 (PostgreSQL)")
print("   2. SQLAlchemy engine")
print("   3. pandas with SQLAlchemy")
print("   4. sqlite3")
print("\n📁 Table name: bill_of_source_Item_description")
print("📊 Ready for 6,207 records from your Excel file")
