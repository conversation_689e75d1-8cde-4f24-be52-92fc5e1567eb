-- SQL script to create a table with name and description columns
-- Based on the Excel file: Copy of Multilevel_BOS_Report_Collector_revS7530VB_level.xlsx
-- 
-- Column mapping from Excel (starting at row 10):
-- - Column 6: "Name" 
-- - Column 12: "Description"
-- - Data starts from row 11

-- Drop table if it exists (optional - remove if you want to keep existing data)
-- DROP TABLE IF EXISTS items_name_description;

-- Create the items table with name and description
CREATE TABLE items_name_description (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE items_name_description IS 'Items table containing names and descriptions from the Multilevel BOS Report';
COMMENT ON COLUMN items_name_description.id IS 'Auto-generated unique identifier';
COMMENT ON COLUMN items_name_description.name IS 'Item name from Name column (Column 6 in Excel)';
COMMENT ON COLUMN items_name_description.description IS 'Item description from Description column (Column 12 in Excel)';
COMMENT ON COLUMN items_name_description.created_date IS 'Timestamp when the record was created';
COMMENT ON COLUMN items_name_description.updated_date IS 'Timestamp when the record was last updated';

-- Create indexes for better performance
CREATE INDEX idx_items_name ON items_name_description(name);
CREATE INDEX idx_items_description_text ON items_name_description(description);
CREATE INDEX idx_items_created_date ON items_name_description(created_date);

-- Create a unique constraint on name and description combination to avoid exact duplicates
CREATE UNIQUE INDEX idx_items_name_desc_unique ON items_name_description(name, description);

-- Example of the data structure that will be inserted:
-- INSERT INTO items_name_description (name, description) VALUES 
-- ('M70012RJ', 'Sample description for M70012RJ'),
-- ('5582359', 'Sample description for 5582359');

-- Note: The actual INSERT statements will be generated by the PowerShell extraction script
