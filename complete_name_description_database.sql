-- Complete SQL script to create and populate the name and description table
-- Based on the Excel file: Copy of Multilevel_BOS_Report_Collector_revS7530VB_level.xlsx
-- 
-- This script:
-- 1. Creates the items_name_description table with name and description columns
-- 2. Includes all the data extracted from the Excel file starting at row 10
-- 
-- Data source analysis:
-- - Excel file has 6,217 total rows
-- - Headers are located in row 10
-- - Data starts from row 11
-- - Column 6: "Name" (item name)
-- - Column 12: "Description" (item description)
-- - Extracted 3,723 unique records
-- - Skipped 2,484 duplicate records
-- - No empty name records found

-- =====================================================
-- STEP 1: CREATE TABLE STRUCTURE
-- =====================================================

-- Drop table if it exists (optional - remove if you want to keep existing data)
-- DROP TABLE IF EXISTS items_name_description;

-- Create the items table with name and description
CREATE TABLE items_name_description (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE items_name_description IS 'Items table containing names and descriptions from the Multilevel BOS Report';
COMMENT ON COLUMN items_name_description.id IS 'Auto-generated unique identifier';
COMMENT ON COLUMN items_name_description.name IS 'Item name from Name column (Column 6 in Excel)';
COMMENT ON COLUMN items_name_description.description IS 'Item description from Description column (Column 12 in Excel)';
COMMENT ON COLUMN items_name_description.created_date IS 'Timestamp when the record was created';
COMMENT ON COLUMN items_name_description.updated_date IS 'Timestamp when the record was last updated';

-- Create indexes for better performance
CREATE INDEX idx_items_name ON items_name_description(name);
CREATE INDEX idx_items_description_text ON items_name_description(description);
CREATE INDEX idx_items_created_date ON items_name_description(created_date);

-- Create a unique constraint on name and description combination to avoid exact duplicates
CREATE UNIQUE INDEX idx_items_name_desc_unique ON items_name_description(name, description);

-- =====================================================
-- STEP 2: INSERT DATA
-- =====================================================

-- Note: The actual INSERT statements are in the file 'insert_name_description_data.sql'
-- You can either:
-- 1. Run this script first, then run insert_name_description_data.sql
-- 2. Or copy the INSERT statements from insert_name_description_data.sql and append them here

-- To load the data, execute the following command after running this script:
-- \i insert_name_description_data.sql  (for PostgreSQL)
-- SOURCE insert_name_description_data.sql;  (for MySQL)
-- .read insert_name_description_data.sql  (for SQLite)

-- =====================================================
-- STEP 3: VERIFICATION QUERIES
-- =====================================================

-- Check the total number of records
-- SELECT COUNT(*) as total_records FROM items_name_description;

-- Check for any duplicate name-description combinations (should be 0 due to unique constraint)
-- SELECT name, description, COUNT(*) as count 
-- FROM items_name_description 
-- GROUP BY name, description 
-- HAVING COUNT(*) > 1;

-- Sample data preview
-- SELECT * FROM items_name_description LIMIT 10;

-- Search for specific items by name (example)
-- SELECT * FROM items_name_description WHERE name LIKE '%M70012RJ%';

-- Search for specific items by description (example)
-- SELECT * FROM items_name_description WHERE description LIKE '%Sand%';

-- Get items with empty descriptions
-- SELECT * FROM items_name_description WHERE description IS NULL OR description = '';

-- =====================================================
-- ADDITIONAL NOTES
-- =====================================================

-- 1. The name column uses VARCHAR(200) to accommodate various name formats
-- 2. The description column uses TEXT for longer descriptions
-- 3. Auto-incrementing ID provides a unique primary key
-- 4. Timestamps are included for audit purposes
-- 5. Indexes are created for better query performance on name and description
-- 6. Unique constraint prevents exact duplicate name-description combinations
-- 7. All data has been extracted from Excel starting at row 10 and deduplicated

-- Statistics from extraction:
-- - Total unique records: 3,723
-- - Duplicate records skipped: 2,484
-- - Empty name records skipped: 0
-- - Source file: Copy of Multilevel_BOS_Report_Collector_revS7530VB_level.xlsx
-- - Generated on: 2025-09-15
