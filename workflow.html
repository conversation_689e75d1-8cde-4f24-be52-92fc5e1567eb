<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced eBOM Creation Pipeline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Computer Modern', 'Latin Modern Roman', 'Times New Roman', serif;
            background: #fafafa;
            padding: 20px;
            color: #333;
        }
        
        .paper-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .paper-title {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        
        .workflow-container {
            position: relative;
            background: linear-gradient(to right, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 40px;
            min-height: 500px;
        }
        
        /* Three columns layout */
        .workflow-columns {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 40px;
        }
        
        .column {
            flex: 1;
            position: relative;
        }
        
        .column-header {
            font-weight: bold;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #6c757d;
            margin-bottom: 20px;
            text-align: center;
        }
        
        /* Input/Output nodes */
        .node {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .node:hover {
            border-color: #007bff;
            box-shadow: 0 4px 12px rgba(0,123,255,0.15);
            transform: translateY(-2px);
        }
        
        .node-title {
            font-weight: 600;
            font-size: 13px;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .node-desc {
            font-size: 11px;
            color: #6c757d;
            line-height: 1.4;
        }
        
        .node-problem {
            color: #dc3545;
            font-size: 11px;
            font-style: italic;
            margin-top: 5px;
        }
        
        .node-benefit {
            color: #28a745;
            font-size: 11px;
            font-style: italic;
            margin-top: 5px;
        }
        
        /* Central processing unit */
        .central-hub {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 30px;
            color: white;
            box-shadow: 0 10px 30px rgba(102,126,234,0.3);
            position: relative;
            min-height: 400px;
        }
        
        .hub-title {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 25px;
        }
        
        .hub-core {
            background: rgba(255,255,255,0.15);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .processing-steps {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .step {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 6px;
            border-left: 3px solid #ffd700;
            font-size: 12px;
        }
        
        /* Connection lines */
        .connection {
            position: absolute;
            width: 2px;
            background: linear-gradient(90deg, #dee2e6, #667eea, #dee2e6);
            height: 100%;
            top: 0;
        }
        
        .connection.left {
            left: 33%;
        }
        
        .connection.right {
            right: 33%;
        }
        
        /* Metrics section */
        .metrics-section {
            display: flex;
            justify-content: space-around;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #dee2e6;
        }
        
        .metric {
            text-align: center;
        }
        
        .metric-value {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
        }
        
        .metric-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        /* ML badge */
        .ml-badge {
            display: inline-block;
            background: #ffd700;
            color: #333;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        /* Animated flow dots */
        @keyframes flow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        .flow-indicator {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
            animation: flow 3s linear infinite;
        }
    </style>
</head>
<body>
    <div class="paper-container">
        <h1 class="paper-title">Unified Data Structure for Enhanced eBOM Creation</h1>
        
        <div class="workflow-container">
            <div class="connection left"></div>
            <div class="connection right"></div>
            
            <div class="workflow-columns">
                <!-- Input Column -->
                <div class="column">
                    <div class="column-header">Data Sources</div>
                    
                    <div class="node">
                        <div class="node-title">Engineering BOM</div>
                        <div class="node-desc">Excel-based product definition</div>
                        <div class="node-problem">✗ Lacks structure & hierarchy</div>
                    </div>
                    
                    <div class="node">
                        <div class="node-title">GON/PLM Cloud</div>
                        <div class="node-desc">Product lifecycle management</div>
                        <div class="node-problem">✗ Incomplete definitions</div>
                    </div>
                    
                    <div class="node">
                        <div class="node-title">PCM Documents</div>
                        <div class="node-desc">PDF-based configurations</div>
                        <div class="node-problem">✗ Requires expert knowledge</div>
                    </div>
                    
                    <div class="node">
                        <div class="node-title">PIM Documents</div>
                        <div class="node-desc">Product information PDFs</div>
                        <div class="node-problem">✗ No functional decomposition</div>
                    </div>
                    
                    <div class="node">
                        <div class="node-title">Supplier/Weights</div>
                        <div class="node-desc">External data sources</div>
                        <div class="node-problem">✗ Fragmented information</div>
                    </div>
                </div>
                
                <!-- Processing Column -->
                <div class="column">
                    <div class="column-header">Intelligence Layer</div>
                    
                    <div class="central-hub">
                        <div class="hub-title">UNIFIED DATA STRUCTURE</div>
                        
                        <div class="ml-badge">Machine Learning Core</div>
                        
                        <div class="hub-core">
                            <div class="processing-steps">
                                <div class="step">1. VLM Extraction (Qwen/CLIP)</div>
                                <div class="step">2. Spectral Clustering (k=4)</div>
                                <div class="step">3. Hierarchical Analysis (11 levels)</div>
                                <div class="step">4. Modular Family Creation</div>
                                <div class="step">5. Validation (89% accuracy)</div>
                            </div>
                        </div>
                        
                        <div style="text-align: center; margin-top: 20px;">
                            <small style="opacity: 0.9;">
                                Silhouette Score: 0.71<br>
                                Platform Ratio: 68%<br>
                                Processing: O(n³)
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Output Column -->
                <div class="column">
                    <div class="column-header">Deliverables</div>
                    
                    <div class="node">
                        <div class="node-title">Enhanced eBOM</div>
                        <div class="node-desc">Complete hierarchical structure</div>
                        <div class="node-benefit">✓ Physical & functional links</div>
                    </div>
                    
                    <div class="node">
                        <div class="node-title">Product Families</div>
                        <div class="node-desc">4 optimized modular families</div>
                        <div class="node-benefit">✓ 68% commonality achieved</div>
                    </div>
                    
                    <div class="node">
                        <div class="node-title">FBI Data Lake</div>
                        <div class="node-desc">Spare parts analytics</div>
                        <div class="node-benefit">✓ Predictive maintenance</div>
                    </div>
                    
                    <div class="node">
                        <div class="node-title">Multi-Level BoS</div>
                        <div class="node-desc">Complete bill of source</div>
                        <div class="node-benefit">✓ Full traceability</div>
                    </div>
                    
                    <div class="node">
                        <div class="node-title">Extended BoM</div>
                        <div class="node-desc">Comprehensive materials list</div>
                        <div class="node-benefit">✓ 23% cost reduction</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Metrics Section -->
        <div class="metrics-section">
            <div class="metric">
                <div class="metric-value">23%</div>
                <div class="metric-label">Cost Reduction</div>
            </div>
            <div class="metric">
                <div class="metric-value">89%</div>
                <div class="metric-label">Accuracy</div>
            </div>
            <div class="metric">
                <div class="metric-value">4</div>
                <div class="metric-label">Product Families</div>
            </div>
            <div class="metric">
                <div class="metric-value">68%</div>
                <div class="metric-label">Platform Ratio</div>
            </div>
            <div class="metric">
                <div class="metric-value">11</div>
                <div class="metric-label">Hierarchy Levels</div>
            </div>
        </div>
    </div>
</body>
</html>