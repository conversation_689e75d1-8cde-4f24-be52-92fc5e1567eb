# PowerShell script to extract name and description from Excel file
# and generate SQL INSERT statements

param(
    [string]$ExcelPath = "Copy of Multilevel_BOS_Report_Collector_revS7530VB_level.xlsx",
    [string]$OutputFile = "insert_name_description_data.sql"
)

try {
    Write-Host "Starting Excel data extraction for name and description columns..."
    
    # Create Excel COM object
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    # Open the workbook
    $workbook = $excel.Workbooks.Open((Resolve-Path $ExcelPath).Path)
    $worksheet = $workbook.Worksheets.Item(1)
    
    # Get the used range
    $usedRange = $worksheet.UsedRange
    $rowCount = $usedRange.Rows.Count
    
    Write-Host "Total rows in Excel: $rowCount"
    Write-Host "Headers are in row 10, data starts from row 11"
    Write-Host "Extracting Column 6 (Name) and Column 12 (Description)"
    
    # Initialize output
    $sqlStatements = @()
    $sqlStatements += "-- SQL INSERT statements for name and description columns"
    $sqlStatements += "-- File: $ExcelPath"
    $sqlStatements += "-- Generated on: $(Get-Date)"
    $sqlStatements += "-- Column 6: Name, Column 12: Description"
    $sqlStatements += ""
    $sqlStatements += "-- Clear existing data (optional - uncomment if needed)"
    $sqlStatements += "-- DELETE FROM items_name_description;"
    $sqlStatements += ""
    $sqlStatements += "-- Insert data from Excel file"
    
    # Track unique items to avoid duplicates
    $uniqueItems = @{}
    $duplicateCount = 0
    $processedCount = 0
    $skippedEmptyCount = 0
    
    # Process data rows (starting from row 11)
    for ($row = 11; $row -le $rowCount; $row++) {
        # Get name from column 6
        $name = $worksheet.Cells.Item($row, 6).Value2
        # Get description from column 12
        $description = $worksheet.Cells.Item($row, 12).Value2
        
        # Skip rows with empty name (description can be empty)
        if (-not $name) {
            $skippedEmptyCount++
            continue
        }
        
        # Convert to string and clean up
        $name = $name.ToString().Trim()
        $description = if ($description) { $description.ToString().Trim() } else { "" }
        
        # Escape single quotes for SQL
        $name = $name -replace "'", "''"
        $description = $description -replace "'", "''"
        
        # Check for duplicates (based on name and description combination)
        $key = "$name|$description"
        if ($uniqueItems.ContainsKey($key)) {
            $duplicateCount++
            continue
        }
        
        $uniqueItems[$key] = $true
        $processedCount++
        
        # Create SQL INSERT statement
        if ($description -eq "") {
            $sqlStatement = "INSERT INTO items_name_description (name, description) VALUES ('$name', NULL);"
        } else {
            $sqlStatement = "INSERT INTO items_name_description (name, description) VALUES ('$name', '$description');"
        }
        $sqlStatements += $sqlStatement
        
        # Progress indicator
        if ($processedCount % 100 -eq 0) {
            Write-Host "Processed $processedCount records..."
        }
    }
    
    # Add final statistics
    $sqlStatements += ""
    $sqlStatements += "-- Statistics:"
    $sqlStatements += "-- Total unique records: $processedCount"
    $sqlStatements += "-- Duplicate records skipped: $duplicateCount"
    $sqlStatements += "-- Empty name records skipped: $skippedEmptyCount"
    $sqlStatements += "-- Generated on: $(Get-Date)"
    
    # Write to output file
    $sqlStatements | Out-File -FilePath $OutputFile -Encoding UTF8
    
    Write-Host ""
    Write-Host "Extraction completed successfully!"
    Write-Host "Unique records processed: $processedCount"
    Write-Host "Duplicate records skipped: $duplicateCount"
    Write-Host "Empty name records skipped: $skippedEmptyCount"
    Write-Host "SQL file generated: $OutputFile"
    
    # Close and cleanup
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($excel) {
        try {
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        } catch {}
    }
}
