# Complete Database with Row Number, Name, and Description (All Duplicates Preserved)

## Overview
Successfully created a SQL database table with `row_number`, `name`, and `description` columns based on data extracted from the Excel file `Copy of Multilevel_BOS_Report_Collector_revS7530VB_level.xlsx`, starting from row 10 as requested. **ALL DUPLICATES ARE PRESERVED** - no deduplication was performed.

## Files Created

### 1. `create_full_table_with_duplicates.sql`
- Contains the SQL DDL statements to create the items_full_data table
- Includes table structure, comments, and indexes
- **No unique constraints** to allow duplicates
- Ready to execute in any SQL database

### 2. `extract_full_data_with_duplicates.ps1`
- PowerShell script that extracts row_number, name, and description data from Excel
- Processes data starting from row 11 (headers in row 10)
- **Preserves ALL duplicates** - no deduplication performed
- Handles NULL values appropriately
- Generates SQL INSERT statements for all records

### 3. `insert_full_data_with_duplicates.sql`
- Contains **6,207 SQL INSERT statements** with ALL the data
- Generated automatically from the Excel file
- Includes statistics and metadata
- **All duplicates preserved**

### 4. `complete_full_database_with_duplicates.sql`
- Comprehensive script that combines table creation and usage instructions
- Includes verification queries for analyzing duplicates
- Complete documentation of the process

## Excel File Analysis

### File Structure
- **Total Rows**: 6,217
- **Header Row**: Row 10 (as specified)
- **Data Start**: Row 11
- **Records Processed**: 6,207 (all available data)

### Column Mapping (from Row 10 headers)
- **Column 1**: "Row Number" → `row_number`
- **Column 6**: "Name" → `name`
- **Column 12**: "Description" → `description`

### Data Statistics
- **Total Records Extracted**: 6,207
- **Duplicate Records**: PRESERVED (not removed)
- **Records with NULL row_number**: 0
- **Records with NULL name**: 0
- **Records with NULL description**: 0
- **Completely empty records skipped**: 0
- **Success Rate**: 100% (all valid data extracted)

## Database Table Structure

```sql
CREATE TABLE items_full_data (
    id SERIAL PRIMARY KEY,
    row_number INTEGER,
    name VARCHAR(200),
    description TEXT,
    excel_row_position INTEGER,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Features
- **Auto-incrementing ID**: Primary key for unique database record identification
- **Row Number**: Original Excel row numbers (Column 1)
- **Name Column**: VARCHAR(200) for item names (Column 6)
- **Description Column**: TEXT for longer descriptions (Column 12)
- **Excel Row Position**: Reference to actual Excel file row for debugging
- **NO Unique Constraints**: Allows all duplicates to be preserved
- **Indexes**: Created on all searchable columns for performance
- **Timestamps**: Audit trail with creation and update dates
- **Comments**: Full documentation of columns and purpose

## Sample Data
```sql
INSERT INTO items_full_data (row_number, name, description, excel_row_position) VALUES 
(1, 'M70012RJ', 'Sand Bag', 11),
(2, '5582359', 'Sandbag Collector', 12),
(3, '5628480', 'Sandbag - 1.65kg -', 13),
(4, '5628479', 'Sandbag - 0.5kg -', 14),
(5, 'M7001NA', '1.5T TDI Posterior Array', 15);
```

## How to Use

### Option 1: Step-by-Step
1. Run `create_full_table_with_duplicates.sql` to create the table structure
2. Run `insert_full_data_with_duplicates.sql` to populate all 6,207 records

### Option 2: All-in-One
1. Review `complete_full_database_with_duplicates.sql` for the complete process
2. Copy INSERT statements from `insert_full_data_with_duplicates.sql` if needed

### Option 3: Re-extract Data
1. Use `extract_full_data_with_duplicates.ps1` to re-process the Excel file
2. Modify the script if you need different columns or processing

## Verification Queries

After creating and populating the table, you can verify and analyze the data:

```sql
-- Check total records
SELECT COUNT(*) as total_records FROM items_full_data;

-- Preview sample data
SELECT * FROM items_full_data ORDER BY row_number LIMIT 10;

-- Find duplicate row numbers (expected)
SELECT row_number, COUNT(*) as count 
FROM items_full_data 
GROUP BY row_number 
HAVING COUNT(*) > 1
ORDER BY count DESC, row_number;

-- Find duplicate names (expected)
SELECT name, COUNT(*) as count 
FROM items_full_data 
GROUP BY name 
HAVING COUNT(*) > 1
ORDER BY count DESC;

-- Search by name
SELECT * FROM items_full_data WHERE name LIKE '%M70012RJ%';

-- Search by description
SELECT * FROM items_full_data WHERE description LIKE '%Sand%';

-- Get all records for a specific row number
SELECT * FROM items_full_data WHERE row_number = 1;

-- Find all duplicates for a specific name
SELECT * FROM items_full_data WHERE name = 'M70012RJ' ORDER BY row_number;
```

## Key Features of This Extraction

1. **Complete Data**: All 6,207 records from Excel extracted
2. **All Duplicates Preserved**: No deduplication performed as requested
3. **Three Columns**: Row Number, Name, and Description as specified
4. **Excel Row Tracking**: Additional column to track original Excel positions
5. **No Data Loss**: 100% of valid data preserved
6. **Flexible Querying**: Indexes support efficient searching and analysis
7. **Duplicate Analysis**: Queries provided to analyze duplicate patterns

## Duplicate Analysis Capabilities

Since duplicates are preserved, you can now:
- Analyze duplicate patterns in the source data
- Track how many times each item appears
- Identify which row numbers have duplicates
- Maintain complete traceability to the original Excel file
- Perform data quality analysis on the source

## Success Metrics

✅ **Complete**: Successfully extracted ALL 6,207 records from Excel  
✅ **Unfiltered**: ALL duplicates preserved as requested  
✅ **Three Columns**: Row Number, Name, and Description extracted  
✅ **Structured**: Created proper database table with indexes  
✅ **Documented**: Full documentation and analysis queries provided  
✅ **Traceable**: Excel row positions tracked for debugging  
✅ **Verified**: No NULL values, no data loss  

The database table is now ready for use with complete, unfiltered data from the Excel file including all duplicates.
