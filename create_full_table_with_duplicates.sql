-- SQL script to create a table with row_number, name and description columns
-- Based on the Excel file: Copy of Multilevel_BOS_Report_Collector_revS7530VB_level.xlsx
-- 
-- Column mapping from Excel (starting at row 10):
-- - Column 1: "Row Number" 
-- - Column 6: "Name" 
-- - Column 12: "Description"
-- - Data starts from row 11
-- - KEEPING ALL DUPLICATES (no deduplication)

-- Drop table if it exists (optional - remove if you want to keep existing data)
-- DROP TABLE IF EXISTS items_full_data;

-- Create the items table with row_number, name and description
CREATE TABLE items_full_data (
    id SERIAL PRIMARY KEY,
    row_number INTEGER,
    name VARCHAR(200),
    description TEXT,
    excel_row_position INTEGER,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE items_full_data IS 'Items table containing row numbers, names and descriptions from the Multilevel BOS Report (with all duplicates preserved)';
COMMENT ON COLUMN items_full_data.id IS 'Auto-generated unique identifier for database records';
COMMENT ON COLUMN items_full_data.row_number IS 'Row Number from Excel Column 1';
COMMENT ON COLUMN items_full_data.name IS 'Item name from Name column (Column 6 in Excel)';
COMMENT ON COLUMN items_full_data.description IS 'Item description from Description column (Column 12 in Excel)';
COMMENT ON COLUMN items_full_data.excel_row_position IS 'Actual row position in Excel file (for reference)';
COMMENT ON COLUMN items_full_data.created_date IS 'Timestamp when the record was created';
COMMENT ON COLUMN items_full_data.updated_date IS 'Timestamp when the record was last updated';

-- Create indexes for better performance
CREATE INDEX idx_items_row_number ON items_full_data(row_number);
CREATE INDEX idx_items_name_full ON items_full_data(name);
CREATE INDEX idx_items_description_full ON items_full_data(description);
CREATE INDEX idx_items_excel_row ON items_full_data(excel_row_position);
CREATE INDEX idx_items_created_date_full ON items_full_data(created_date);

-- Note: NO unique constraints since we want to preserve all duplicates

-- Example of the data structure that will be inserted:
-- INSERT INTO items_full_data (row_number, name, description, excel_row_position) VALUES 
-- (1, 'M70012RJ', 'Sand Bag', 11),
-- (2, '5582359', 'Sandbag Collector', 12),
-- (3, '5628480', 'Sandbag - 1.65kg -', 13);

-- Note: The actual INSERT statements will be generated by the PowerShell extraction script
