-- Create table: bill_of_source_Item_description
-- Ready to execute in notebook with database connection

DROP TABLE IF EXISTS bill_of_source_Item_description;

CREATE TABLE bill_of_source_Item_description (
    id SERIAL PRIMARY KEY,
    row_number INTEGER,
    name VARCHAR(200),
    description TEXT,
    excel_row_position INTEGER,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_bos_row_number ON bill_of_source_Item_description(row_number);
CREATE INDEX idx_bos_name ON bill_of_source_Item_description(name);
CREATE INDEX idx_bos_description ON bill_of_source_Item_description(description);

-- Verify table creation
SELECT 'Table bill_of_source_Item_description created successfully!' as status;
