#!/usr/bin/env python3
"""
Example usage of the ColPali + DeepSeek R2 RAG system.

This script demonstrates how to:
1. Initialize the RAG system
2. Index document images
3. Query the system for information

Make sure you have document images in PNG/JPG format to test with.
"""

from colpali_deepseek import ColPaliDeepSeekRAG
from PIL import Image
import os

def create_sample_document():
    """Create a simple sample document for testing."""
    # Create a simple white image with some text-like patterns
    img = Image.new('RGB', (800, 600), color='white')
    
    # You could add text using PIL's ImageDraw if needed
    # For now, this is just a placeholder
    
    sample_path = "sample_document.png"
    img.save(sample_path)
    print(f"Created sample document: {sample_path}")
    return sample_path

def main():
    """Main example function."""
    print("🚀 Initializing ColPali + DeepSeek R2 RAG System...")
    
    try:
        # Initialize the RAG system
        rag_system = ColPaliDeepSeekRAG()
        print("✅ System initialized successfully!")
        
        # Check if we have any document images to work with
        document_files = []
        for ext in ['*.png', '*.jpg', '*.jpeg']:
            import glob
            document_files.extend(glob.glob(ext))
        
        if not document_files:
            print("📄 No document images found. Creating a sample document...")
            sample_doc = create_sample_document()
            document_files = [sample_doc]
        
        # Index the documents
        print(f"\n📚 Indexing {len(document_files)} document(s)...")
        for i, doc_path in enumerate(document_files):
            doc_id = f"doc_{i+1}"
            print(f"  Indexing {doc_path} as {doc_id}...")
            rag_system.index_document(doc_path, doc_id)
        
        print("✅ All documents indexed!")
        
        # Example queries
        queries = [
            "What is the main topic of this document?",
            "Can you summarize the key points?",
            "What information is presented in the document?"
        ]
        
        print("\n🔍 Running example queries...")
        for query in queries:
            print(f"\n❓ Query: {query}")
            result = rag_system.query_documents(query, top_k=2)
            
            print(f"📋 Retrieved documents: {result['retrieved_documents']}")
            print(f"💬 Response: {result['response']}")
            print("-" * 50)
        
        print("\n🎉 Example completed successfully!")
        print("\nYou can now:")
        print("1. Add your own document images to the current directory")
        print("2. Run this script again to index and query them")
        print("3. Modify the queries in this script to test different questions")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure you have enough GPU memory (or use CPU)")
        print("2. Check that all dependencies are installed correctly")
        print("3. Ensure you have internet connection for model downloads")

if __name__ == "__main__":
    main()
