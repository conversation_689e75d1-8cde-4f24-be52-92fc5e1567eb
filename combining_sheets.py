import pandas as pd

def read_and_combine_sheets(excel_file):
    """
    Combine all sheets from Excel file where data starts at row 10
    """
    # Read all sheets
    xl_file = pd.ExcelFile(excel_file)
    
    dataframes = []
    for i, sheet_name in enumerate(xl_file.sheet_names):
        # Read sheet starting from row 10
        df = pd.read_excel(excel_file, sheet_name=sheet_name, skiprows=9)
        
        # Standardize column name
        if 'Item Number' in df.columns:
            df.rename(columns={'Item Number': 'Item_Number'}, inplace=True)
        
        # Add sheet identifier to track source
        df['Source_Sheet'] = sheet_name
        
        # Add to list
        dataframes.append(df)
        
        print(f"Sheet '{sheet_name}': {len(df)} rows")
    
    # Combine all dataframes
    # First sheet (with most elements) as base
    result = dataframes[0]
    
    # Join others
    for i, df in enumerate(dataframes[1:], start=1):
        # Keep columns you need
        cols_to_merge = ['Item_Number', 'Name'] + [col for col in df.columns 
                                                     if col not in ['Item_Number', 'Name', 'Source_Sheet']]
        
        result = pd.merge(result, 
                         df[cols_to_merge], 
                         on='Item_Number', 
                         how='outer', 
                         suffixes=('', f'_{i}'))
    
    return result

# Use it
print("Starting to read and combine sheets...")
combined_df = read_and_combine_sheets(r'C:\Users\<USER>\Documents\augment-projects\VLM\Extended BOM Voyager.xlsx')
print(f"Combined dataframe shape: {combined_df.shape}")

# Handle Name columns - combine them if needed
# If you want to consolidate multiple Name columns into one
name_cols = [col for col in combined_df.columns if col.startswith('Name')]
if len(name_cols) > 1:
    # Combine Name columns - take first non-null value
    combined_df['Name_Combined'] = combined_df[name_cols].bfill(axis=1).iloc[:, 0]

# Save
combined_df.to_excel('combined_output.xlsx', index=False)