-- =====================================================
-- COMPLETE SQL SCRIPT FOR DATABASE EXECUTION
-- Table Name: bill_of_source_Item_description
-- =====================================================
-- 
-- This script creates the table and provides instructions for data insertion
-- Run this script directly on your database
-- 
-- Source: Copy of Multilevel_BOS_Report_Collector_revS7530VB_level.xlsx
-- Records: 6,207 (all duplicates preserved)
-- Columns: Row Number, Name, Description

-- =====================================================
-- STEP 1: CREATE TABLE
-- =====================================================

-- Drop table if it exists (uncomment if you want to recreate)
-- DROP TABLE IF EXISTS bill_of_source_Item_description;

CREATE TABLE bill_of_source_Item_description (
    id SERIAL PRIMARY KEY,
    row_number INTEGER,
    name VARCHAR(200),
    description TEXT,
    excel_row_position INTEGER,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add table and column comments
COMMENT ON TABLE bill_of_source_Item_description IS 'Bill of Source Items with descriptions from Multilevel BOS Report (all duplicates preserved)';
COMMENT ON COLUMN bill_of_source_Item_description.id IS 'Auto-generated unique identifier';
COMMENT ON COLUMN bill_of_source_Item_description.row_number IS 'Row Number from Excel Column 1';
COMMENT ON COLUMN bill_of_source_Item_description.name IS 'Item name from Excel Column 6';
COMMENT ON COLUMN bill_of_source_Item_description.description IS 'Item description from Excel Column 12';
COMMENT ON COLUMN bill_of_source_Item_description.excel_row_position IS 'Original Excel row position';
COMMENT ON COLUMN bill_of_source_Item_description.created_date IS 'Record creation timestamp';
COMMENT ON COLUMN bill_of_source_Item_description.updated_date IS 'Record update timestamp';

-- Create indexes for performance
CREATE INDEX idx_bos_row_number ON bill_of_source_Item_description(row_number);
CREATE INDEX idx_bos_name ON bill_of_source_Item_description(name);
CREATE INDEX idx_bos_description ON bill_of_source_Item_description(description);
CREATE INDEX idx_bos_excel_row ON bill_of_source_Item_description(excel_row_position);
CREATE INDEX idx_bos_created_date ON bill_of_source_Item_description(created_date);

-- =====================================================
-- STEP 2: VERIFY TABLE CREATION
-- =====================================================

-- Check if table was created successfully
SELECT 'Table bill_of_source_Item_description created successfully!' as status;

-- Show table structure
\d bill_of_source_Item_description;

-- =====================================================
-- STEP 3: DATA INSERTION INSTRUCTIONS
-- =====================================================

-- The table is now ready for data insertion!
-- 
-- To insert the 6,207 records from your Excel file:
-- 
-- OPTION 1: Run the separate INSERT file
-- Execute the file: insert_bill_of_source_data.sql
-- This file contains all 6,207 INSERT statements
-- 
-- OPTION 2: Copy and paste INSERT statements
-- Open insert_bill_of_source_data.sql and copy all INSERT statements
-- Then paste and execute them in your database
-- 
-- OPTION 3: Use database import tools
-- Many databases support CSV import - you could export Excel to CSV
-- Then use COPY (PostgreSQL) or LOAD DATA (MySQL) commands

-- =====================================================
-- STEP 4: VERIFICATION QUERIES (run after data insertion)
-- =====================================================

-- After inserting data, run these queries to verify:

-- Check total records
-- SELECT COUNT(*) as total_records FROM bill_of_source_Item_description;

-- Preview first 10 records
-- SELECT * FROM bill_of_source_Item_description ORDER BY row_number LIMIT 10;

-- Check for duplicates (expected since we preserved them)
-- SELECT row_number, COUNT(*) as count 
-- FROM bill_of_source_Item_description 
-- GROUP BY row_number 
-- HAVING COUNT(*) > 1
-- ORDER BY count DESC
-- LIMIT 10;

-- Search examples
-- SELECT * FROM bill_of_source_Item_description WHERE name LIKE '%M70012RJ%';
-- SELECT * FROM bill_of_source_Item_description WHERE description LIKE '%Sand%';

-- =====================================================
-- SUMMARY
-- =====================================================

-- ✅ Table 'bill_of_source_Item_description' created
-- ✅ Indexes created for performance
-- ✅ Comments added for documentation
-- ✅ Ready for 6,207 data records
-- 
-- Next step: Execute insert_bill_of_source_data.sql to load your data
-- 
-- Table structure:
-- - id: Auto-incrementing primary key
-- - row_number: Excel row numbers (preserves duplicates)
-- - name: Item names from Excel
-- - description: Item descriptions from Excel
-- - excel_row_position: Original Excel row reference
-- - created_date: Timestamp when record was created
-- - updated_date: Timestamp when record was updated

SELECT 'Setup complete! Ready to insert 6,207 records.' as next_step;
