# PowerShell script to extract item_id and item_description from Excel file
# and generate SQL INSERT statements

param(
    [string]$ExcelPath = "Copy of Multilevel_BOS_Report_Collector_revS7530VB_level.xlsx",
    [string]$OutputFile = "insert_items_data.sql"
)

try {
    Write-Host "Starting Excel data extraction..."
    
    # Create Excel COM object
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    # Open the workbook
    $workbook = $excel.Workbooks.Open((Resolve-Path $ExcelPath).Path)
    $worksheet = $workbook.Worksheets.Item(1)
    
    # Get the used range
    $usedRange = $worksheet.UsedRange
    $rowCount = $usedRange.Rows.Count
    
    Write-Host "Total rows in Excel: $rowCount"
    Write-Host "Data starts from row 11"
    
    # Initialize output
    $sqlStatements = @()
    $sqlStatements += "-- SQL INSERT statements generated from Excel file"
    $sqlStatements += "-- File: $ExcelPath"
    $sqlStatements += "-- Generated on: $(Get-Date)"
    $sqlStatements += ""
    $sqlStatements += "-- Clear existing data (optional - uncomment if needed)"
    $sqlStatements += "-- DELETE FROM items;"
    $sqlStatements += ""
    $sqlStatements += "-- Insert data from Excel file"
    
    # Track unique items to avoid duplicates
    $uniqueItems = @{}
    $duplicateCount = 0
    $processedCount = 0
    
    # Process data rows (starting from row 11)
    for ($row = 11; $row -le $rowCount; $row++) {
        # Get item_id from column 3 (Mark Number)
        $itemId = $worksheet.Cells.Item($row, 3).Value2
        # Get item_description from column 6 (Name)
        $itemDescription = $worksheet.Cells.Item($row, 6).Value2
        
        # Skip rows with empty data
        if (-not $itemId -or -not $itemDescription) {
            continue
        }
        
        # Convert to string and clean up
        $itemId = $itemId.ToString().Trim()
        $itemDescription = $itemDescription.ToString().Trim()
        
        # Escape single quotes for SQL
        $itemId = $itemId -replace "'", "''"
        $itemDescription = $itemDescription -replace "'", "''"
        
        # Check for duplicates
        $key = "$itemId|$itemDescription"
        if ($uniqueItems.ContainsKey($key)) {
            $duplicateCount++
            continue
        }
        
        $uniqueItems[$key] = $true
        $processedCount++
        
        # Create SQL INSERT statement
        $sqlStatement = "INSERT INTO items (item_id, item_description) VALUES ('$itemId', '$itemDescription');"
        $sqlStatements += $sqlStatement
        
        # Progress indicator
        if ($processedCount % 100 -eq 0) {
            Write-Host "Processed $processedCount records..."
        }
    }
    
    # Add final statistics
    $sqlStatements += ""
    $sqlStatements += "-- Statistics:"
    $sqlStatements += "-- Total unique records: $processedCount"
    $sqlStatements += "-- Duplicate records skipped: $duplicateCount"
    $sqlStatements += "-- Generated on: $(Get-Date)"
    
    # Write to output file
    $sqlStatements | Out-File -FilePath $OutputFile -Encoding UTF8
    
    Write-Host ""
    Write-Host "Extraction completed successfully!"
    Write-Host "Unique records processed: $processedCount"
    Write-Host "Duplicate records skipped: $duplicateCount"
    Write-Host "SQL file generated: $OutputFile"
    
    # Close and cleanup
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($excel) {
        try {
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        } catch {}
    }
}
