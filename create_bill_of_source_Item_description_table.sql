-- SQL script to create the bill_of_source_Item_description table
-- Based on the Excel file: Copy of Multilevel_BOS_Report_Collector_revS7530VB_level.xlsx
-- 
-- This table contains:
-- - Row Number (from Column 1)
-- - Name (from Column 6) 
-- - Description (from Column 12)
-- - <PERSON><PERSON> duplicates preserved
-- - 6,207 total records

-- =====================================================
-- CREATE TABLE: bill_of_source_Item_description
-- =====================================================

-- Drop table if it exists (optional - uncomment if you want to recreate)
-- DROP TABLE IF EXISTS bill_of_source_Item_description;

-- Create the bill_of_source_Item_description table
CREATE TABLE bill_of_source_Item_description (
    id SERIAL PRIMARY KEY,
    row_number INTEGER,
    name VARCHA<PERSON>(200),
    description TEXT,
    excel_row_position INTEGER,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE bill_of_source_Item_description IS 'Bill of Source Items with descriptions from Multilevel BOS Report (all duplicates preserved)';
COMMENT ON COLUMN bill_of_source_Item_description.id IS 'Auto-generated unique identifier for database records';
COMMENT ON COLUMN bill_of_source_Item_description.row_number IS 'Row Number from Excel Column 1';
COMMENT ON COLUMN bill_of_source_Item_description.name IS 'Item name from Name column (Column 6 in Excel)';
COMMENT ON COLUMN bill_of_source_Item_description.description IS 'Item description from Description column (Column 12 in Excel)';
COMMENT ON COLUMN bill_of_source_Item_description.excel_row_position IS 'Actual row position in Excel file (for reference)';
COMMENT ON COLUMN bill_of_source_Item_description.created_date IS 'Timestamp when the record was created';
COMMENT ON COLUMN bill_of_source_Item_description.updated_date IS 'Timestamp when the record was last updated';

-- Create indexes for better performance
CREATE INDEX idx_bos_row_number ON bill_of_source_Item_description(row_number);
CREATE INDEX idx_bos_name ON bill_of_source_Item_description(name);
CREATE INDEX idx_bos_description ON bill_of_source_Item_description(description);
CREATE INDEX idx_bos_excel_row ON bill_of_source_Item_description(excel_row_position);
CREATE INDEX idx_bos_created_date ON bill_of_source_Item_description(created_date);

-- =====================================================
-- VERIFICATION QUERIES (run after data insertion)
-- =====================================================

-- Uncomment these queries after inserting data to verify the table:

-- Check total number of records
-- SELECT COUNT(*) as total_records FROM bill_of_source_Item_description;

-- Preview first 10 records
-- SELECT * FROM bill_of_source_Item_description ORDER BY row_number LIMIT 10;

-- Check for duplicate row numbers
-- SELECT row_number, COUNT(*) as count 
-- FROM bill_of_source_Item_description 
-- GROUP BY row_number 
-- HAVING COUNT(*) > 1
-- ORDER BY count DESC, row_number
-- LIMIT 10;

-- Check for duplicate names
-- SELECT name, COUNT(*) as count 
-- FROM bill_of_source_Item_description 
-- GROUP BY name 
-- HAVING COUNT(*) > 1
-- ORDER BY count DESC
-- LIMIT 10;

-- Search for specific items
-- SELECT * FROM bill_of_source_Item_description WHERE name LIKE '%M70012RJ%';
-- SELECT * FROM bill_of_source_Item_description WHERE description LIKE '%Sand%';

-- =====================================================
-- TABLE CREATION COMPLETE
-- =====================================================

-- Table 'bill_of_source_Item_description' has been created successfully!
-- 
-- Next steps:
-- 1. Run the INSERT statements from 'insert_bill_of_source_data.sql'
-- 2. Or manually insert your data using the structure above
-- 
-- Table structure:
-- - id: Auto-incrementing primary key
-- - row_number: Excel row numbers (Column 1)
-- - name: Item names (Column 6)
-- - description: Item descriptions (Column 12)
-- - excel_row_position: Original Excel row for reference
-- - created_date: Record creation timestamp
-- - updated_date: Record update timestamp
