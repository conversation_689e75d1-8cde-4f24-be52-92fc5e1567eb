-- SQL script to create a table with item_id and item_description columns
-- Based on the Excel file: Copy of Multilevel_BOS_Report_Collector_revS7530VB_level.xlsx
--
-- Analysis of the Excel file structure:
-- - Headers are located in row 10
-- - Data starts from row 11
-- - Column 3: "Mark Number" (will be used as item_id)
-- - Column 6: "Name" (will be used as item_description)
-- - Total rows: 6217, Total columns: 19

-- Drop table if it exists (optional - remove if you want to keep existing data)
-- DROP TABLE IF EXISTS items;

-- Create the items table
CREATE TABLE items (
    item_id VARCHAR(100) PRIMARY KEY,
    item_description VARCHAR(500) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE items IS 'Items table containing item IDs and descriptions from the Multilevel BOS Report';
COMMENT ON COLUMN items.item_id IS 'Item identifier from Mark Number column (Column 3 in Excel)';
COMMENT ON COLUMN items.item_description IS 'Item description from Name column (Column 6 in Excel)';
COMMENT ON COLUMN items.created_date IS 'Timestamp when the record was created';
COMMENT ON COLUMN items.updated_date IS 'Timestamp when the record was last updated';

-- Create indexes for better performance
CREATE INDEX idx_items_description ON items(item_description);
CREATE INDEX idx_items_created_date ON items(created_date);

-- Example of how to insert data from the Excel file
-- Based on the sample data observed:
-- INSERT INTO items (item_id, item_description) VALUES
-- ('6', 'M70012RJ'),
-- ('1', '5582359'),
-- ('2', '5628480'),
-- ('1', '5628479'),
-- ('9', 'M7001NA');

-- Note: You'll need to extract the actual data from the Excel file
-- The data starts from row 11 and you need columns 3 (Mark Number) and 6 (Name)
-- Make sure to handle any duplicate item_ids appropriately
